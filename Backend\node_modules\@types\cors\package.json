{"name": "@types/cors", "version": "2.8.19", "description": "TypeScript definitions for cors", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "pluma", "url": "https://github.com/pluma"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77", "url": "https://github.com/gtpan77"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cors"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "a090e558c5f443573318c2955deecddc840bd8dfaac7cdedf31c7f6ede8d0b47", "typeScriptVersion": "5.1"}