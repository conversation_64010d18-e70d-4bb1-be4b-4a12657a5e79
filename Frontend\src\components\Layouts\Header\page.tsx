"use client";
import React, { useState, useRef, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faHome,
  faTachometerAlt,
  faBookOpen,
  faCertificate,
  faBell,
  faGraduationCap,
  faUser,
  faSignOutAlt,
  faChevronDown,
} from "@fortawesome/free-solid-svg-icons";
import NotificationDropdown from "@/components/NotificationDropdown";
import { Button } from "@/components/ui/button";

import { useRouter } from "next/navigation";
import { GraduationCap } from "lucide-react";

interface User {
  id: number;
  email: string;
  username: string;
  role: string;
  first_name: string;
  last_name: string;
}

const Navbar: React.FC = ({sidebarExpanded}:any) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Function to decode JWT token
  const decodeJWT = (token: string) => {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map(function (c) {
            return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join("")
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error decoding JWT:", error);
      return null;
    }
  };

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Get user data from token
  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      const decodedToken = decodeJWT(token);
      if (decodedToken) {
        setUser({
          id: decodedToken.id,
          email: decodedToken.email,
          username: decodedToken.username,
          role: decodedToken.role,
          first_name: decodedToken.first_name || decodedToken.username,
          last_name: decodedToken.last_name || "",
        });
      }
    }
  }, []);

  // Generate user initials
  const getUserInitials = (firstName: string, lastName: string) => {
    const first = firstName ? firstName.charAt(0).toUpperCase() : "";
    const last = lastName ? lastName.charAt(0).toUpperCase() : "";
    return first + last || "U";
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("token");
    sessionStorage.removeItem("token");
    localStorage.removeItem("authToken");
    sessionStorage.removeItem("authToken");
    setUser(null);
    if (isMounted) {
      router.push("/signin");
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-30">
      <div className="container mx-auto flex justify-between items-center py-1 px-4">
        {/* Logo */}
        {!sidebarExpanded && (
        <div className="flex items-center space-x-3 absolute left-2">
        {/* Icon Container */}
        <div className="bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg">
            {/* Graduation Cap Icon */}
            <GraduationCap className="w-6 h-6 text-white" />
        </div>
         {/* Text Logo */}
                <div className="flex items-center">
                  <span className="text-orange-500 text-lg font-bold">EX</span>
                  <span className="text-[#005071] text-lg font-bold">Learn</span>
                </div>
        </div>)}
        {/* <div className="flex items-center text-orange-500 text-2xl font-bold">
          <FontAwesomeIcon icon={faGraduationCap} className="mr-2 text-2xl" />
          AI Trainer
        </div> */}
        {/* Search Bar */}
        <div className="flex-grow flex justify-center mx-4">
          <div className="relative w-full max-w-xs">
            {/* <Button
              variant="outline"
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 rounded-full border border-gray-300 focus:ring-orange-200 focus:border-orange-300"
            >
              <span className="flex-grow text-left">Search courses, topics, or instructors...</span>
            </Button> */}
          </div>
        </div>
        {/* Right section: Notifications & Profile */}
        <div className="flex items-center space-x-4">
          <NotificationDropdown />
          {/* Profile Dropdown */}
          <div className="relative" ref={dropdownRef}>
            {user ? (
              <>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 px-2 py-1 rounded-full hover:bg-orange-50"
                  onClick={toggleDropdown}
                >
                  <span className="bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium">
                    {getUserInitials(user.first_name, user.last_name)}
                  </span>
                  <span className="text-gray-700 text-sm font-medium">{user.first_name}</span>
                  <FontAwesomeIcon
                    icon={faChevronDown}
                    className={`text-gray-500 text-xs transition-transform duration-200 ${isDropdownOpen ? "rotate-180" : ""}`}
                  />
                </Button>
                {isDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in">
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center space-x-3">
                        <span className="bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium">
                          {getUserInitials(user.first_name, user.last_name)}
                        </span>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {user.first_name} {user.last_name}
                          </p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    </div>
                    <div className="py-1">
                      <Button
                        variant="ghost"
                        className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50"
                        onClick={() => {
                          setIsDropdownOpen(false);
                        }}
                      >
                        <FontAwesomeIcon icon={faUser} className="mr-3 text-gray-400" />
                        Profile
                      </Button>
                      <Button
                        variant="ghost"
                        className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                        onClick={handleLogout}
                      >
                        <FontAwesomeIcon icon={faSignOutAlt} className="mr-3 text-red-500" />
                        Log out
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <Button asChild className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                <a href="/signin" className="flex items-center space-x-2">
                  <FontAwesomeIcon icon={faUser} className="text-sm" />
                  <span className="text-sm font-medium">Sign In</span>
                </a>
              </Button>
            )}
          </div>
        </div>
      </div>
      {/* Secondary Navigation */}
      {/* <div className="container mx-auto mt-2">
        <ul className="flex space-x-8 text-sm">
          <li>
            <a href="/" className="flex items-center space-x-2 text-orange-500 border-b-2 border-orange-500 pb-2 font-semibold">
              <FontAwesomeIcon icon={faHome} className="text-sm" />
              <span>Home</span>
            </a>
          </li>
          <li>
            <a href="/my-dashboard" className="flex items-center space-x-2 text-gray-700 hover:text-orange-500 hover:border-b-2 hover:border-orange-500 pb-2 transition-all">
              <FontAwesomeIcon icon={faTachometerAlt} className="text-sm" />
              <span>My Dashboard</span>
            </a>
          </li>
          <li>
            <a href="/my-learning" className="flex items-center space-x-2 text-gray-700 hover:text-orange-500 hover:border-b-2 hover:border-orange-500 pb-2 transition-all">
              <FontAwesomeIcon icon={faBookOpen} className="text-sm" />
              <span>My Learning</span>
            </a>
          </li>
          <li>
            <a href="/my-certificates" className="flex items-center space-x-2 text-gray-700 hover:text-orange-500 hover:border-b-2 hover:border-orange-500 pb-2 transition-all">
              <FontAwesomeIcon icon={faCertificate} className="text-sm" />
              <span>Certificates</span>
            </a>
          </li>
        </ul>
      </div> */}
    </nav>
  );
};

export default Navbar;